export interface SocialVideoSchema {
  title: string;
  hashtags: string[];
  description: string;
  platform: string;
  video_id: string;
  video_url: string;
  thumbnail_url: string;
  publish_time: string;
  duration: number;
  view_count: number;
  like_count: number;
  comment_count: number;
  share_count: number;
}

export interface TiktokVideoAuthorSchema {
  nickname: string;
  unique_id: string;
  sec_uid: string;
  // We ignore this because it's usually empty
  // account_region: string;
  // User engagement/quality rating, but usually they are 1 when we can search their videos
  // user_rate: number;
  // Ignored because it's usually 0
  // commerce_user_level: number;
  // Ignored because it's usually False, i guess this is live stremaing
  // live_commerce: boolean;
  // Ignore these two, mostly are false
  // with_shop_entry: boolean;
  // with_commerce_entry: number;
  //Account age (established vs. new creators)
  create_time: number;
  region: string;
  language: string;
  signature: string;
  follower_count: number;
  avatar_url: string;
  user_tags: string;
  youtube_channel_id: string;
  ins_id: string;
  twitter_id: string;
}

export interface TiktokVideoSchema extends SocialVideoSchema {
  author: TiktokVideoAuthorSchema;
}

export interface TiktokChallengeSchema {
  challenge_id: string;
  challenge_name: string;
  use_count: number;
  user_count: number;
  view_count: number;
}

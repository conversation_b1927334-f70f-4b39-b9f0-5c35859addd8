import { TiktokServiceSchema } from '@/schemas/tools_schema';
import { TiktokChallengeSchema, TiktokVideoSchema } from '@repo/constants';
import { TikHubService } from './tikhub';
import { TokAPIService } from './tokapi';
import { scoutDbService, workflowDbService } from '../index';
import { env } from '@/lib/env';

/**
 * Service for scouting videos from different platforms and updating the database
 */
export class VideoScouterService {
  private tiktokServices: TiktokServiceSchema[];
  private serviceMap: Record<string, TiktokServiceSchema>;

  constructor() {
    // Initialize all available TikTok services
    const tikHubService = new TikHubService();
    const tokAPIService = new TokAPIService();

    this.serviceMap = {
      tikhub: tikHubService,
      tokapi: tokAPIService,
    };

    // Order services based on preference
    this.tiktokServices = this.orderServicesByPreference([
      tikHubService,
      tokAPIService,
    ]);

    console.log(
      `TikTok services initialized. Order: ${this.tiktokServices
        .map((service) => service.constructor.name)
        .join(' -> ')}`,
    );
  }

  /**
   * Order services based on the preferred service configuration
   * @param services Array of available services
   * @returns Ordered array with preferred service first
   */
  private orderServicesByPreference(
    services: TiktokServiceSchema[],
  ): TiktokServiceSchema[] {
    const preferredService = env.PREFERRED_TIKTOK_SERVICE;

    if (!preferredService) {
      console.log(
        'No preferred TikTok service configured, using default order',
      );
      return services;
    }

    const preferred = this.serviceMap[preferredService];
    if (!preferred) {
      console.warn(
        `Preferred service '${preferredService}' not found, using default order`,
      );
      return services;
    }

    console.log(
      `Using preferred TikTok service: ${preferred.constructor.name}`,
    );

    // Put preferred service first, then the rest
    const otherServices = services.filter((service) => service !== preferred);
    return [preferred, ...otherServices];
  }

  /**
   * Get information about the current service configuration
   * @returns Service configuration info
   */
  getServiceInfo() {
    const preferredService = env.PREFERRED_TIKTOK_SERVICE;
    const serviceOrder = this.tiktokServices.map(
      (service) => service.constructor.name,
    );

    return {
      preferredService: preferredService || 'none',
      availableServices: Object.keys(this.serviceMap),
      serviceOrder,
      totalServices: this.tiktokServices.length,
    };
  }

  /**
   * Search for TikTok videos directly
   * @param keyword The keyword to search for
   * @param offset Pagination offset
   * @param count Number of results to return
   * @param sort_type Sort type (0: relevance, 1: most liked)
   * @param publish_time Publish time (0: unlimited, 1: last 24 hours, 7: past week, 30: past month, 90: past 3 months, 180: past 6 months)
   */
  async searchTiktokVideos(
    keyword: string,
    offset = 0,
    count = 20,
    sort_type = 0,
    publish_time = 0,
  ) {
    console.log(`Searching TikTok videos for keyword: "${keyword}"`);

    // Try each service until one succeeds
    for (const [index, service] of this.tiktokServices.entries()) {
      const serviceName = service.constructor.name;
      const isPreferred = index === 0 && env.PREFERRED_TIKTOK_SERVICE;

      try {
        console.log(
          `Attempting video search with ${serviceName}${isPreferred ? ' (preferred)' : ''}`,
        );

        const result = await service.searchVideos(
          keyword,
          offset,
          count,
          sort_type,
          publish_time,
        );

        console.log(
          `Successfully searched videos with ${serviceName}, found ${result?.length || 0} videos`,
        );

        return result;
      } catch (error) {
        console.error(
          `Error searching videos with service ${serviceName}:`,
          error,
        );
        // Continue to the next service
      }
    }

    throw new Error('All TikTok services failed to search videos');
  }

  /**
   * Search for TikTok challenges/hashtags
   * @param keyword The keyword to search for
   * @param offset Pagination offset
   * @param count Number of results to return
   */
  async searchTiktokChallenges(keyword: string, offset = 0, count = 20) {
    console.log(`Searching TikTok challenges for keyword: "${keyword}"`);

    // Try each service until one succeeds
    for (const [index, service] of this.tiktokServices.entries()) {
      const serviceName = service.constructor.name;
      const isPreferred = index === 0 && env.PREFERRED_TIKTOK_SERVICE;

      try {
        console.log(
          `Attempting challenge search with ${serviceName}${isPreferred ? ' (preferred)' : ''}`,
        );

        const result = await service.searchHashtag(keyword, offset, count);

        console.log(
          `Successfully searched challenges with ${serviceName}, found ${result?.length || 0} challenges`,
        );

        return result;
      } catch (error) {
        console.error(
          `Error searching hashtag with service ${serviceName}:`,
          error,
        );
        // Continue to the next service
      }
    }

    throw new Error('All TikTok services failed to search hashtags');
  }

  /**
   * Get videos for a specific TikTok hashtag and update the database
   * @param challengeId The challenge/hashtag ID
   * @param cursor Pagination cursor
   * @param count Number of results to return
   */
  async scoutTiktokHashtagVideos(challengeId: string, cursor = 0, count = 20) {
    console.log(`Getting hashtag videos for challenge ID: "${challengeId}"`);

    let videos: TiktokVideoSchema[] = [];
    let usedService: string = '';

    // Try each service until one succeeds
    for (const [index, service] of this.tiktokServices.entries()) {
      const serviceName = service.constructor.name;
      const isPreferred = index === 0 && env.PREFERRED_TIKTOK_SERVICE;

      try {
        console.log(
          `Attempting hashtag videos fetch with ${serviceName}${isPreferred ? ' (preferred)' : ''}`,
        );

        videos = await service.getHashtagVideos(challengeId, cursor, count);
        usedService = serviceName;

        console.log(
          `Successfully fetched hashtag videos with ${serviceName}, found ${videos?.length || 0} videos`,
        );

        break;
      } catch (error) {
        console.error(
          `Error getting hashtag videos with service ${serviceName}:`,
          error,
        );
        // Continue to the next service
      }
    }

    if (videos.length === 0) {
      throw new Error('All TikTok services failed to get hashtag videos');
    }

    // Process each video and update the database
    const processedVideoIds: number[] = [];
    for (const video of videos) {
      try {
        // console.log('Processing video:', video.title);
        const videoId = await scoutDbService.processTikTokVideo(video);
        processedVideoIds.push(videoId);
        console.log('Video processed video id:', videoId);
      } catch (error) {
        console.error('Error processing video:', error);
        // Continue with the next video
      }
    }

    console.log(
      `Video processing complete using ${usedService}. Processed ${processedVideoIds.length} videos.`,
    );

    return {
      videos,
      processedVideoIds,
    };
  }

  /**
   * Search for challenges based on keywords and find the best ones by view count
   * @param keywords Array of keywords to search for
   * @param traceId Workflow run ID for context tracking
   * @param minChallenges Minimum number of challenges to find
   */
  async findBestChallenges(
    keywords: string[],
    traceId: string,
    minChallenges = 5,
  ) {
    console.log(`Finding best challenges for keywords: ${keywords.join(', ')}`);

    const allChallenges: TiktokChallengeSchema[] = [];

    // Search for challenges for each keyword
    for (const keyword of keywords) {
      try {
        const challenges = await this.searchTiktokChallenges(keyword, 0, 20);
        console.log(
          `Found ${challenges.length} challenges for keyword "${keyword}"`,
        );
        allChallenges.push(...challenges);
      } catch (error) {
        console.error(
          `Error searching challenges for keyword "${keyword}":`,
          error,
        );
      }
    }

    // Sort challenges by view count (descending)
    const sortedChallenges = allChallenges
      .sort((a, b) => b.view_count - a.view_count)
      // Remove duplicates based on challenge_id
      .filter(
        (challenge, index, self) =>
          index ===
          self.findIndex((c) => c.challenge_id === challenge.challenge_id),
      );

    // Take at least minChallenges or all if fewer are available
    const bestChallenges = sortedChallenges.slice(
      0,
      Math.max(minChallenges, sortedChallenges.length),
    );

    console.log(`Selected ${bestChallenges.length} best challenges`);

    // Save to context
    const contextData = {
      keywords,
      challenges: bestChallenges,
      timestamp: new Date().toISOString(),
    };

    await workflowDbService.saveWorkflowContext(
      traceId,
      'best_challenges',
      contextData,
    );

    return bestChallenges;
  }

  /**
   * Fetch videos from the best challenges and collect at least 100 good videos
   * @param traceId Workflow run ID for context tracking
   * @param minVideos Minimum number of videos to collect (default: 100)
   */
  async collectGoodVideos(traceId: string, minVideos = 100) {
    console.log(
      `Collecting at least ${minVideos} good videos for trace ID: ${traceId}`,
    );

    // Get challenges from context
    const contextData = await workflowDbService.getWorkflowContext(
      traceId,
      'best_challenges',
    );

    if (!contextData || !contextData.contextData) {
      throw new Error(
        'No challenges found in context. Run findBestChallenges first.',
      );
    }

    const contextDataObj = contextData.contextData as {
      challenges: TiktokChallengeSchema[];
    };

    if (
      !contextDataObj.challenges ||
      !Array.isArray(contextDataObj.challenges)
    ) {
      throw new Error('Invalid challenge data in context');
    }

    const challenges = contextDataObj.challenges;
    console.log(`Found ${challenges.length} challenges in context`);

    const allVideos: TiktokVideoSchema[] = [];
    const processedVideoIds: number[] = [];

    // Fetch videos from each challenge until we have enough
    for (const challenge of challenges) {
      if (allVideos.length >= minVideos) {
        break;
      }

      try {
        console.log(
          `Fetching videos for challenge: ${challenge.challenge_name}`,
        );
        // Get more videos per challenge to reach our target faster
        const result = await this.scoutTiktokHashtagVideos(
          challenge.challenge_id,
          0,
          25,
        );

        allVideos.push(...result.videos);
        processedVideoIds.push(...result.processedVideoIds);

        console.log(`Collected ${allVideos.length}/${minVideos} videos so far`);
      } catch (error) {
        console.error(
          `Error fetching videos for challenge ${challenge.challenge_name}:`,
          error,
        );
      }
    }

    // Sort videos by view count (descending)
    const sortedVideos = allVideos.sort((a, b) => b.view_count - a.view_count);

    // Save to context
    const contextData2 = {
      challenges,
      videos: sortedVideos.map((video) => ({
        video_id: video.video_id,
        title: video.title,
        view_count: video.view_count,
        like_count: video.like_count,
        comment_count: video.comment_count,
        author: {
          unique_id: video.author.unique_id,
          nickname: video.author.nickname,
        },
      })),
      processedVideoIds,
      timestamp: new Date().toISOString(),
    };

    await workflowDbService.saveWorkflowContext(
      traceId,
      'good_videos',
      contextData2,
    );

    return {
      videos: sortedVideos,
      processedVideoIds,
      totalVideos: sortedVideos.length,
    };
  }
}

// Export a singleton instance
export const videoScouter = new VideoScouterService();
